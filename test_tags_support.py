#!/usr/bin/env python3
"""
Test script to verify that tags support has been added to braintrust.init()
"""

import sys
import os

# Add the src directory to the path so we can import braintrust
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'py', 'src'))

try:
    import braintrust
    
    # Test 1: Check if tags parameter is accepted by init function
    print("Testing tags parameter in braintrust.init()...")
    
    # This should not raise an error if tags parameter is properly supported
    experiment = braintrust.init(
        project="test-project",
        experiment="test-experiment", 
        tags=["test-tag", "experiment-tag"],
        # Use a fake API key for testing - this will fail at login but that's OK
        api_key="fake-key-for-testing"
    )
    
    print("✅ Tags parameter accepted by braintrust.init()")
    
    # Test 2: Check if tags parameter is in the function signature
    import inspect
    sig = inspect.signature(braintrust.init)
    if 'tags' in sig.parameters:
        print("✅ Tags parameter found in braintrust.init() signature")
        param = sig.parameters['tags']
        print(f"   Parameter type: {param.annotation}")
        print(f"   Default value: {param.default}")
    else:
        print("❌ Tags parameter NOT found in braintrust.init() signature")
        
    print("\nTest completed successfully! Tags support has been added.")
    
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
